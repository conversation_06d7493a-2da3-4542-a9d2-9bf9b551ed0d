# sandbox_client.py (New Async Version)

import os
import json
import logging
import asyncio # For run_in_executor if needed for CPU-bound tasks
from typing import Dict, List, Any, Optional, Union
try:
    import aiohttp
    print(f"[DEBUG CL] aiohttp version: {aiohttp.__version__}" )
except Exception as import_err:
    print(f"[ERROR CL] Failed to import aiohttp just before SandboxClient init: {import_err}" )

import pandas as pd # Assuming pandas is used for dataframe upload

# Configure logging
logging.basicConfig(level=logging.INFO) # Consider making level configurable
logger = logging.getLogger("sandbox_client_async") # Changed logger name slightly

class SandboxClient:
    """
    Asynchronous Client for interacting with the Python Sandbox API.
    """
    def __init__(self, base_url: str = "http://localhost:8004" ):
        self.base_url = base_url
        self._http_session: Optional[aiohttp.ClientSession] = None
        self.sessions: Dict[str, Dict[str, Any]] = {}
        # logger.info(f"Initialized Async SandboxClient with base URL: {base_url}" ) # Temporarily commented out
        print(f"[DEBUG SANDBOX] SandboxClient __init__ called with base_url: {base_url}")

    async def _get_http_session(self) -> aiohttp.ClientSession:
        if self._http_session is None or self._http_session.closed:
            # You might want to pass connector settings here, e.g., for SSL
            self._http_session = aiohttp.ClientSession()
        return self._http_session

    async def close(self):
        """Close the underlying aiohttp client session."""
        if self._http_session and not self._http_session.closed:
            await self._http_session.close()
            self._http_session = None
            logger.info("Closed aiohttp ClientSession.")

    async def create_session(self, session_id: Optional[str] = None) -> Dict[str, Any]:
        if session_id and session_id in self.sessions:
            logger.info(f"Using cached session: {session_id}")
            return self.sessions[session_id]
        
        session = await self._get_http_session()
        url = f"{self.base_url}/sessions"
        params = {}
        if session_id:
            params["session_id"] = session_id
        
        try:
            logger.info(f"Creating new session with ID: {session_id or 'auto-generated'}")
            async with session.post(url, params=params) as response:
                response.raise_for_status() # Raises an exception for 4xx/5xx errors
                session_data = await response.json()
                self.sessions[session_data["session_id"]] = session_data
                logger.info(f"Session created: {session_data['session_id']}")
                return session_data
        except aiohttp.ClientError as e:
            logger.error(f"Failed to create session (aiohttp.ClientError): {e}")
            return {"error": f"Failed to create session: {str(e)}"}
        except Exception as e: # Catch other potential errors like JSONDecodeError
            logger.error(f"Failed to create session (Exception): {e}")
            return {"error": f"Failed to create session: {str(e)}"}


    async def execute_code(self, 
                           session_id: str, 
                           code: str, 
                           timeout_seconds: int = 30) -> Dict[str, Any]:
        if session_id not in self.sessions:
            # Attempt to create/retrieve session if not in local cache
            # This could happen if the client instance was recreated or session expired on server
            await self.get_session_info(session_id) # Try to fetch it, which also caches
            if session_id not in self.sessions: # If still not found after trying to get info
                 logger.warning(f"Session {session_id} not found before executing code, attempting to create.")
                 create_result = await self.create_session(session_id)
                 if "error" in create_result:
                     return create_result # Return error from creation

        session = await self._get_http_session()
        url = f"{self.base_url}/sessions/{session_id}/execute"
        payload = {"code": code, "timeout_seconds": timeout_seconds}
        
        try:
            logger.info(f"Executing code in session {session_id}")
            async with session.post(url, json=payload) as response:
                response.raise_for_status()
                result = await response.json()
                logger.info(f"Code execution completed in session {session_id}")
                return result
        except aiohttp.ClientError as e:
            logger.error(f"Failed to execute code (aiohttp.ClientError): {str(e)}")
            return {"success": False, "error": f"Failed to execute code: {str(e)}", "output": f"Error: {str(e)}"}
        except Exception as e:
            logger.error(f"Failed to execute code (Exception): {str(e)}")
            return {"success": False, "error": f"Failed to execute code: {str(e)}", "output": f"Error: {str(e)}"}

    async def upload_file(self, 
                          session_id: str, 
                          file_path: str, 
                          variable_name: Optional[str] = None) -> Dict[str, Any]:
        if session_id not in self.sessions:
            await self.get_session_info(session_id)
            if session_id not in self.sessions:
                logger.warning(f"Session {session_id} not found before uploading file, attempting to create.")
                create_result = await self.create_session(session_id)
                if "error" in create_result: return create_result

        session = await self._get_http_session()
        url = f"{self.base_url}/sessions/{session_id}/upload"
        
        data = aiohttp.FormData()
        try:
            # Note: aiohttp typically wants an opened file object for 'file' part.
            # Or, if file_path is small, you can read its content.
            # For large files, streaming upload is better but more complex with FormData.
            # Here, we read the file content.
            with open(file_path, "rb") as f_sync:
                file_content = f_sync.read()
            
            data.add_field('file', file_content, filename=os.path.basename(file_path), content_type='application/octet-stream') # Adjust content_type if known
            if variable_name:
                data.add_field('variable_name', variable_name)
            
            logger.info(f"Uploading file {file_path} to session {session_id}")
            async with session.post(url, data=data) as response:
                response.raise_for_status()
                result = await response.json()
                logger.info(f"File uploaded successfully to session {session_id}")
                return result
        except FileNotFoundError:
            logger.error(f"File not found: {file_path}")
            return {"success": False, "error": f"File not found: {file_path}"}
        except aiohttp.ClientError as e:
            logger.error(f"Failed to upload file (aiohttp.ClientError): {str(e)}")
            return {"success": False, "error": f"Failed to upload file: {str(e)}"}
        except Exception as e:
            logger.error(f"Failed to upload file (Exception): {str(e)}")
            return {"success": False, "error": f"Failed to upload file: {str(e)}"}

    async def upload_json_data(self,
                              session_id: str,
                              headers: List[str],
                              rows: List[Dict[str, Any]],
                              variable_name: str,
                              file_name: Optional[str] = None) -> Dict[str, Any]:
        """Upload JSON data (headers and rows) to create a DataFrame in the session."""
        if session_id not in self.sessions:
            await self.get_session_info(session_id)
            if session_id not in self.sessions:
                logger.warning(f"Session {session_id} not found before uploading JSON data, attempting to create.")
                create_result = await self.create_session(session_id)
                if "error" in create_result:
                    return create_result

        session = await self._get_http_session()
        url = f"{self.base_url}/sessions/{session_id}/upload_json"

        try:
            payload = {
                "headers": headers,
                "rows": rows,
                "variable_name": variable_name
            }
            if file_name:
                payload["file_name"] = file_name

            logger.info(f"Uploading JSON data to session {session_id} with variable name {variable_name}")
            async with session.post(url, json=payload) as response:
                response.raise_for_status()
                result = await response.json()
                logger.info(f"JSON data uploaded successfully to session {session_id}")
                return result
        except aiohttp.ClientError as e:
            logger.error(f"Failed to upload JSON data (aiohttp.ClientError): {str(e)}")
            return {"success": False, "error": f"Failed to upload JSON data: {str(e)}"}
        except Exception as e:
            logger.error(f"Failed to upload JSON data (Exception): {str(e)}")
            return {"success": False, "error": f"Failed to upload JSON data: {str(e)}"}

    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        session = await self._get_http_session()
        url = f"{self.base_url}/sessions/{session_id}"
        
        try:
            logger.info(f"Getting info for session {session_id}")
            async with session.get(url) as response:
                response.raise_for_status()
                session_info = await response.json()
                self.sessions[session_id] = session_info # Update cache
                return session_info
        except aiohttp.ClientError as e:
            logger.error(f"Failed to get session info for {session_id} (aiohttp.ClientError): {str(e)}")
            if session_id in self.sessions: del self.sessions[session_id] # Invalidate cache on error
            return None
        except Exception as e:
            logger.error(f"Failed to get session info for {session_id} (Exception): {str(e)}")
            if session_id in self.sessions: del self.sessions[session_id]
            return None
    
    # list_sessions, delete_session can be similarly converted if needed

    async def download_plot(self, session_id: str, plot_name: str) -> Optional[bytes]:
        """
        Download a plot from a sandbox session. Returns plot data as bytes.
        (Removed save_path for simplicity with async, can be added back if needed)
        """
        # No need to check self.sessions here, as downloading a plot doesn't strictly
        # require the session to be in our client's local cache if the server knows it.
        # However, a get_session_info call could be made first if strict validation is needed.

        session = await self._get_http_session()
        url = f"{self.base_url}/sessions/{session_id}/plots/{plot_name}"
        logger.info(f"[DEBUG PLOT ASYNC] Downloading plot from URL: {url}")
        
        try:
            logger.info(f"[DEBUG PLOT ASYNC] Sending GET request for plot {plot_name} from session {session_id}")
            async with session.get(url) as response:
                response.raise_for_status() # Important to check for HTTP errors
                plot_bytes = await response.read()
                logger.info(f"[DEBUG PLOT ASYNC] Successfully downloaded plot {plot_name} ({len(plot_bytes)} bytes)")
                return plot_bytes
        except aiohttp.ClientError as e: # More specific error handling
            logger.error(f"[ERROR PLOT ASYNC] Failed to download plot {plot_name} (aiohttp.ClientError): {str(e)}")
            # Depending on how you want to handle this in on_message, you might return None or raise
            return None # Or raise SandboxInteractionError(f"Failed to download plot: {str(e)}")
        except Exception as e: # Catch other potential errors
            logger.error(f"[ERROR PLOT ASYNC] Failed to download plot {plot_name} (Exception): {str(e)}")
            return None

    async def list_plots(self, session_id: str) -> list:
        """List all plots in a session."""
        session = await self._get_http_session()
        url = f"{self.base_url}/sessions/{session_id}/plots"
        try:
            async with session.get(url) as response:
                response.raise_for_status()
                data = await response.json()
                return data.get("plots", [])
        except Exception as e:
            logger.error(f"Failed to list plots for session {session_id}: {e}")
            return []

    # --- Methods converted from the original, if needed ---
    async def list_sessions(self) -> List[Dict[str, Any]]:
        session_http = await self._get_http_session()
        url = f"{self.base_url}/sessions"
        try:
            async with session_http.get(url) as response:
                response.raise_for_status()
                sessions_data = await response.json()
                return sessions_data.get("sessions", [])
        except Exception as e:
            logger.error(f"Failed to list sessions: {e}")
            return []

    async def delete_session(self, session_id: str) -> bool:
        session_http = await self._get_http_session()
        url = f"{self.base_url}/sessions/{session_id}"
        try:
            async with session_http.delete(url) as response:
                response.raise_for_status()
                if session_id in self.sessions: del self.sessions[session_id]
                return True
        except Exception as e:
            logger.error(f"Failed to delete session {session_id}: {e}")
            return False

    async def upload_dataframe(self, session_id: str, df: pd.DataFrame, variable_name: str) -> Dict[str, Any]:
        # df.to_csv is synchronous. For very large DFs, this could block.
        # Running it in an executor if it's a concern.
        loop = asyncio.get_event_loop()
        
        # Create a temporary file path (synchronous part)
        import tempfile
        fd, temp_path = tempfile.mkstemp(suffix='.csv')
        os.close(fd) # Close the file descriptor, NamedTemporaryFile handles this better

        try:
            # Perform the CPU-bound/blocking I/O in an executor
            await loop.run_in_executor(None, df.to_csv, temp_path, False) # df.to_csv(path, index=False)
            
            # Now upload the created file asynchronously
            result = await self.upload_file(session_id, temp_path, variable_name)
            return result
        except Exception as e:
            logger.error(f"Failed to upload DataFrame: {str(e)}")
            return {"success": False, "error": f"Failed to upload DataFrame: {str(e)}"}
        finally:
            if os.path.exists(temp_path):
                os.remove(temp_path) # Synchronous os.remove is usually fine for temp files