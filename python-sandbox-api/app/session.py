"""
Session management for the Python Sandbox API.

Handles session creation, cleanup, and resource management.
"""
import asyncio
import os
import sys
import uuid
import shutil
import io
import traceback
import logging
import pickle
import contextlib
import subprocess
import importlib
import psutil
import json
import time
import pandas as pd
import numpy as np
from fastapi import UploadFile
from plotly.graph_objs import Figure as PlotlyFigure
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set

import threading
import uuid as uuid_module

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("sandbox-api")

# Session timeout (inactivity) in seconds
SESSION_TIMEOUT_SECONDS = int(os.environ.get("SESSION_INACTIVITY_TIMEOUT", 180))

# Base directory for all session data
SESSIONS_DIR = os.environ.get("SESSIONS_DIR", "/tmp/sessions")
os.makedirs(SESSIONS_DIR, exist_ok=True)


class Session:
    """
    Represents an isolated Python execution session.
    """
    def __init__(self, session_id: str):
        self.id = session_id
        self.created_at = datetime.now()
        self.last_activity = self.created_at
        self.expires_at = self.last_activity + timedelta(seconds=SESSION_TIMEOUT_SECONDS)

        # Create session root directory
        self.session_dir = os.path.join(SESSIONS_DIR, self.id)
        os.makedirs(self.session_dir, exist_ok=True)

        # Subdirectories for uploaded files and generated plots
        self.files_dir = os.path.join(self.session_dir, "files")
        self.plots_dir = os.path.join(self.session_dir, "plots")
        os.makedirs(self.files_dir, exist_ok=True)
        os.makedirs(self.plots_dir, exist_ok=True)

        # Ensure the shared packages directory is in the Python path
        shared_pkgs_dir = os.environ.get("SHARED_PACKAGES_DIR", "/shared/packages")
        os.makedirs(shared_pkgs_dir, exist_ok=True)
        if shared_pkgs_dir not in sys.path:
            sys.path.insert(0, shared_pkgs_dir)

        # In-memory state
        self.globals: Dict[str, Any] = {}
        self.installed_packages: Set[str] = set()

        # Lock to serialize code execution
        self._lock = asyncio.Lock()

        logger.info(f"Created session {self.id}")

    async def touch(self):
        """Refresh expiration based on activity"""
        self.last_activity = datetime.now()
        self.expires_at = self.last_activity + timedelta(seconds=SESSION_TIMEOUT_SECONDS)

    async def execute_code(self, code: str, timeout_seconds: int = 30) -> Dict:
        """
        Execute Python code in this session and capture output, variables, and plots.
        """
        async with self._lock:
            await self.touch()
            stdout_buffer = io.StringIO()
            stderr_buffer = io.StringIO()

            # Track performance
            start_time = datetime.now()
            process = psutil.Process(os.getpid())
            memory_before = process.memory_info().rss / 1024 / 1024

            # Get existing plots before execution to detect new ones
            existing_plots = set(await self._scan_for_plots())

            # Prepare execution environment
            exec_globals = self.globals.copy()
            exec_globals.update({
                "__builtins__": __builtins__,
                "np": np,
                "pd": pd,
                "session_id": self.id,
                "plots_dir": self.plots_dir,
                "files_dir": self.files_dir
            })

            success = True
            error = None

            try:
                with contextlib.redirect_stdout(stdout_buffer), contextlib.redirect_stderr(stderr_buffer):
                    await asyncio.wait_for(
                        asyncio.to_thread(exec, code, exec_globals),
                        timeout=timeout_seconds
                    )
                # Persist state
                self.globals.update(exec_globals)

                # Clean builtins leaking
                for key in list(self.globals):
                    if key.startswith("__") or key in {"__builtins__", "exit", "quit"}:
                        self.globals.pop(key, None)

            except asyncio.TimeoutError:
                success = False
                error = f"Execution timed out after {timeout_seconds} seconds"
                stderr_buffer.write(f"\n{error}")
            except Exception as e:
                success = False
                error = str(e)
                stderr_buffer.write(f"\n{error}\n")
                stderr_buffer.write(traceback.format_exc())

            # Metrics
            execution_time = (datetime.now() - start_time).total_seconds()
            memory_after = process.memory_info().rss / 1024 / 1024
            memory_usage = memory_after - memory_before

            output = stdout_buffer.getvalue()
            if stderr_buffer.getvalue():
                output += f"\n{stderr_buffer.getvalue()}"

            variables = await self.get_variable_info()

            # Save any Plotly figures defined in globals with thread-safe unique naming
            newly_saved: List[str] = []
            saved_variables: Set[str] = set()  # Track which variables we've already saved
            saved_figure_ids: Set[int] = set()  # Track figure objects by their id() to avoid duplicates

            for var_name, var in exec_globals.items():
                if var_name in saved_variables:
                    continue

                # Handle individual PlotlyFigure objects
                if isinstance(var, PlotlyFigure):
                    figure_id = id(var)
                    if figure_id not in saved_figure_ids:
                        unique_id = str(uuid_module.uuid4())[:8]
                        fname = f"{self.id}_{var_name}_{unique_id}.pickle"
                        path = os.path.join(self.plots_dir, fname)

                        with open(path, 'wb') as f:
                            pickle.dump(var, f)
                        newly_saved.append(fname)
                        saved_figure_ids.add(figure_id)
                    saved_variables.add(var_name)

                # Handle lists/collections of PlotlyFigure objects
                elif isinstance(var, (list, tuple)):
                    plotly_figures_in_list = []
                    for i, item in enumerate(var):
                        if isinstance(item, PlotlyFigure):
                            plotly_figures_in_list.append((i, item))

                    if plotly_figures_in_list:
                        for i, fig in plotly_figures_in_list:
                            figure_id = id(fig)
                            if figure_id not in saved_figure_ids:
                                unique_id = str(uuid_module.uuid4())[:8]
                                fname = f"{self.id}_{var_name}_{i}_{unique_id}.pickle"
                                path = os.path.join(self.plots_dir, fname)

                                with open(path, 'wb') as f:
                                    pickle.dump(fig, f)
                                newly_saved.append(fname)
                                saved_figure_ids.add(figure_id)
                        saved_variables.add(var_name)

            # Detect any new file-based plots in plots_dir (exclude already saved ones)
            current_plots = set(await self._scan_for_plots())
            new_file_plots = list(current_plots - existing_plots - set(newly_saved))

            # Combine all plots (newly saved + new file plots)
            plots = newly_saved + new_file_plots

            return {
                "success": success,
                "output": output,
                "variables": variables,
                "plots": plots,
                "error": error,
                "execution_time": execution_time,
                "memory_usage": memory_usage
            }

    async def get_variable_info(self) -> Dict[str, Any]:
        """
        Describe the session's global variables with enhanced information for DataFrames.

        Returns a dictionary where:
        - For DataFrames: Returns a dict with 'type', 'shape', 'columns', and 'dtypes'
        - For other types: Returns a string description
        """
        info: Dict[str, Any] = {}
        for name, val in self.globals.items():
            if name.startswith("_") or name in {"np", "pd", "plots_dir", "files_dir", "session_id"}:
                continue
            try:
                if isinstance(val, pd.DataFrame):
                    # Enhanced DataFrame info with shape, columns and types
                    info[name] = {
                        "type": "DataFrame",
                        "shape": val.shape,
                        "columns": val.columns.tolist(),
                        "dtypes": {col: str(dtype) for col, dtype in val.dtypes.items()},
                        "description": f"DataFrame: {val.shape[0]}×{val.shape[1]}"
                    }
                elif isinstance(val, np.ndarray):
                    info[name] = f"ndarray: {val.shape} ({val.dtype})"
                elif isinstance(val, PlotlyFigure):
                    info[name] = "PlotlyFigure"
                elif isinstance(val, (list, tuple)):
                    # Check if it's a list of PlotlyFigures
                    plotly_count = sum(1 for item in val if isinstance(item, PlotlyFigure))
                    if plotly_count > 0:
                        info[name] = f"{type(val).__name__}: {len(val)} items ({plotly_count} PlotlyFigures)"
                    else:
                        info[name] = f"{type(val).__name__}: {len(val)} items"
                elif isinstance(val, dict):
                    info[name] = f"dict: {len(val)} keys"
                else:
                    info[name] = type(val).__name__
            except Exception as e:
                info[name] = f"Error retrieving info: {str(e)}"
        return info

    async def _scan_for_plots(self) -> List[str]:
        """List files in the plots directory"""
        return [f for f in os.listdir(self.plots_dir)
                if os.path.isfile(os.path.join(self.plots_dir, f))]

    async def install_library(self, libraries: List[str]) -> Dict:
        """
        Proxy method that delegates to SessionManager's install_library

        Note: This method should be called through the API endpoint which
        properly passes the request to the session_manager instance.
        """
        # This is a placeholder that will be properly connected in main.py
        # The actual implementation is in the SessionManager class
        raise NotImplementedError("This method should be called through the API endpoint")



    async def upload_file(self, file: UploadFile, variable_name: Optional[str] = None) -> Dict:
        """Save an uploaded file and optionally load it into a variable"""
        async with self._lock:
            await self.touch()
            filename = file.filename
            dest = os.path.join(self.files_dir, filename)
            content = await file.read()
            with open(dest, 'wb') as f:
                f.write(content)

            res = {"filename": filename}
            if variable_name:
                try:
                    if filename.lower().endswith('.csv'):
                        self.globals[variable_name] = pd.read_csv(dest)
                        res['variable'] = f"{variable_name} (DataFrame)"
                    else:
                        self.globals[variable_name] = dest
                        res['variable'] = f"{variable_name} (path)"
                except Exception as e:
                    res['error'] = str(e)
            return res

    async def get_file_path(self, filename: str) -> Optional[str]:
        """Resolve an uploaded file or plot path"""
        await self.touch()
        for folder in (self.plots_dir, self.files_dir):
            p = os.path.join(folder, filename)
            if os.path.exists(p):
                return p
        return None

    async def list_files(self) -> List[Dict[str, Any]]:
        """List all files and plots in the session"""
        await self.touch()
        items: List[Dict[str, Any]] = []
        for folder, ftype in [(self.files_dir, 'file'), (self.plots_dir, 'plot')]:
            for fn in os.listdir(folder):
                full = os.path.join(folder, fn)
                if os.path.isfile(full):
                    items.append({
                        'name': fn,
                        'type': ftype,
                        'size': os.path.getsize(full),
                        'created': datetime.fromtimestamp(os.path.getctime(full))
                    })
        return items

    def is_expired(self) -> bool:
        """Check session expiration"""
        return datetime.now() > self.expires_at

    async def cleanup(self):
        """Remove session directory and all its contents"""
        try:
            if os.path.exists(self.session_dir):
                shutil.rmtree(self.session_dir)
            logger.info(f"Cleaned up session {self.id}")
        except Exception as e:
            logger.error(f"Error cleaning up {self.id}: {e}")


class SessionManager:
    """
    Coordinates multiple sessions.
    """
    def __init__(self):
        self.sessions: Dict[str, Session] = {}
        self._lock = asyncio.Lock()
        self._cleanup_task = None

    async def create_session(self, session_id: Optional[str] = None) -> Session:
        async with self._lock:
            sid = session_id or str(uuid.uuid4())
            sess = Session(sid)
            self.sessions[sid] = sess
            return sess

    async def get_session(self, session_id: str) -> Optional[Session]:
        async with self._lock:
            sess = self.sessions.get(session_id)
            if sess and sess.is_expired():
                await self.close_session(session_id)
                return None
            return sess

    async def close_session(self, session_id: str) -> bool:
        async with self._lock:
            sess = self.sessions.pop(session_id, None)
            if sess:
                await sess.cleanup()
                return True
            return False

    async def list_sessions(self) -> List[Dict[str, Any]]:
        async with self._lock:
            return [
                {'session_id': s.id, 'created_at': s.created_at, 'last_activity': s.last_activity, 'expires_at': s.expires_at}
                for s in self.sessions.values() if not s.is_expired()
            ]

    async def start_cleanup_task(self):
        if not self._cleanup_task or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("Started session cleanup task")

    async def _cleanup_loop(self):
        while True:
            await asyncio.sleep(60)
            to_remove = []
            async with self._lock:
                for sid, sess in self.sessions.items():
                    if sess.is_expired():
                        to_remove.append(sid)
            for sid in to_remove:
                await self.close_session(sid)
                logger.info(f"Expired session {sid} cleaned up")

    async def shutdown(self):
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        async with self._lock:
            for sid, sess in list(self.sessions.items()):
                await sess.cleanup()
            self.sessions.clear()
        logger.info("SessionManager shutdown complete")

    # Ajouter cette méthode:
    async def install_library(self, libraries: List[str]) -> Dict:
        """
        Install pip packages globally for all sessions.
        """
        if not libraries:
            return {"success": True, "output": "No libraries specified", "installed": []}

        # Sanitize input
        to_install = [re for re in libraries if re]
        if not to_install:
            return {"success": False, "output": "Invalid library names", "installed": []}

        # Get shared packages directory from environment or use default
        shared_pkgs_dir = os.environ.get("SHARED_PACKAGES_DIR", "/shared/packages")

        # Ensure the directory exists with correct permissions
        os.makedirs(shared_pkgs_dir, exist_ok=True)
        os.system(f"chmod -R 777 {shared_pkgs_dir}")

        # Add the directory to Python path if needed
        if shared_pkgs_dir not in sys.path:
            sys.path.insert(0, shared_pkgs_dir)

        # Create a virtual environment in the shared directory if it doesn't exist
        venv_dir = os.path.join(shared_pkgs_dir, "venv")
        if not os.path.exists(venv_dir):
            import venv
            venv.create(venv_dir, with_pip=True)

        # Get the path to the pip in the virtual environment
        if os.name == 'nt':  # Windows
            pip_path = os.path.join(venv_dir, 'Scripts', 'pip.exe')
        else:  # Unix/Linux
            pip_path = os.path.join(venv_dir, 'bin', 'pip')

        # Install packages using the virtual environment's pip
        cmd = [pip_path, "install", "--no-cache-dir"] + to_install

        proc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        out, err = proc.communicate()
        success = proc.returncode == 0
        installed = []

        # Get the site-packages directory of the virtual environment
        if os.name == 'nt':
            site_packages = os.path.join(venv_dir, 'Lib', 'site-packages')
        else:
            # For Unix/Linux, find the correct Python version directory
            lib_dir = os.path.join(venv_dir, 'lib')
            python_dirs = [d for d in os.listdir(lib_dir) if d.startswith('python')]
            if python_dirs:
                site_packages = os.path.join(lib_dir, python_dirs[0], 'site-packages')
            else:
                site_packages = os.path.join(lib_dir, f'python{sys.version_info.major}.{sys.version_info.minor}', 'site-packages')

        # Add the site-packages to Python path if it exists
        if os.path.exists(site_packages) and site_packages not in sys.path:
            sys.path.insert(0, site_packages)

        # Update installed packages in all sessions
        if success:
            for lib in to_install:
                pkg = lib.split('==')[0]
                installed.append(pkg)
                # Update installed_packages set in all sessions
                for sess in self.sessions.values():
                    sess.installed_packages.add(pkg)

        return {"success": success, "output": out + err, "installed": installed}